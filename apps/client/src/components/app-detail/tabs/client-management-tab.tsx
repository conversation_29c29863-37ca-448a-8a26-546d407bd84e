import { ClientVersions } from '../oem/client-versions'
import type { TabComponentProps } from '../types'

export type PublicVersion = {
  id: string
  version: string
  description: string
  platform: string
  publishedAt: string
}

export function ClientManagementTab({ applicationId, application }: TabComponentProps) {
  return (
    <div className="space-y-6">
      {/* 客户端版本管理 */}
      <ClientVersions applicationId={applicationId} application={application} />
    </div>
  )
}
