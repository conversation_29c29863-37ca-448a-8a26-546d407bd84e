import { useState } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import type { CreateClientVersionInput, OEMConfig } from '@coozf/zod'

export function useOEMManagement(applicationId: string) {
  const [showOEMConfigDialog, setShowOEMConfigDialog] = useState(false)
  const [showCreateVersionDialog, setShowCreateVersionDialog] = useState(false)

  // 获取客户端版本列表
  const { data: clientVersions, refetch: refetchClientVersions } = useQuery(
    trpc.clientVersion.list.queryOptions({
      applicationId,
      page: 1,
      pageSize: 10,
    }),
  )

  // 获取公用基础版本列表（用于创建客户端版本）
  const { data: baseVersions } = useQuery(
    trpc.clientVersion.getLatestPublicVersion.queryOptions({
      applicationId,
      platform: 'WIN',
    }),
  )

  // 更新OEM配置
  const updateOEMConfigMutation = useMutation(
    trpc.clientVersion.updateOEMConfig.mutationOptions({
      onSuccess: () => {
        toast.success('OEM配置更新成功')
        setShowOEMConfigDialog(false)
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }),
  )

  // 创建客户端版本
  const createClientVersionMutation = useMutation(
    trpc.clientVersion.create.mutationOptions({
      onSuccess: () => {
        toast.success('客户端版本创建成功')
        refetchClientVersions()
        setShowCreateVersionDialog(false)
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }),
  )

  const handleOEMConfigSave = (config: OEMConfig) => {
    updateOEMConfigMutation.mutate({
      ...config,
      applicationId,
    })
  }

  const handleCreateVersion = (data: CreateClientVersionInput) => {
    createClientVersionMutation.mutate({
      ...data,
      applicationId,
    })
  }

  return {
    // 对话框状态
    showOEMConfigDialog,
    setShowOEMConfigDialog,
    showCreateVersionDialog,
    setShowCreateVersionDialog,

    // 数据
    clientVersions,
    baseVersions,

    // 请求状态
    updateOEMConfigMutation,
    createClientVersionMutation,

    // 处理函数
    handleCreateVersion,
    handleOEMConfigSave,
    refetchClientVersions,
  }
}
