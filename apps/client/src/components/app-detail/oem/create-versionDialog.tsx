import { useForm } from 'react-hook-form'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import type { PublicVersion } from '../tabs/client-management-tab'
import { useMutation } from '@tanstack/react-query'
import { queryClient, trpc } from '@/lib/trpc'
import { toast } from '@coozf/ui/lib/utils'
import { LoadingButton } from '@coozf/ui/components/loading'

interface CreateVersionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  applicationId: string
  baseVersion: PublicVersion
}

export function CreateVersionDialog({ open, onOpenChange, applicationId, baseVersion }: CreateVersionDialogProps) {
  const form = useForm({
    defaultValues: {
      description: baseVersion.description,
    },
  })

  console.log(baseVersion)

  const createClientVersionMutation = useMutation(
    trpc.clientVersion.create.mutationOptions({
      onSuccess: () => {
        toast.success('客户端版本创建成功')
        onOpenChange(false)
        queryClient.invalidateQueries({
          queryKey: trpc.clientVersion.list.queryKey({ applicationId }),
        })
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }),
  )

  const onSubmit = (data: any) => {
    const { gitlabProjectId, ...createVersionData } = data

    createClientVersionMutation.mutate({
      ...createVersionData,
      applicationId,
      baseVersionId: baseVersion.id,
      platform: baseVersion.platform,
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>创建客户端版本</DialogTitle>
          <DialogDescription>基于公用版本创建您的定制客户端</DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="baseVersionId">基础版本 *</Label>
            <div>{baseVersion.version}</div>
            <div>{baseVersion.platform}</div>
            <div>{baseVersion.publishedAt}</div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">版本描述</Label>
            <Input id="description" {...form.register('description')} placeholder="请输入版本描述" />
            {form.formState.errors.description && (
              <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <LoadingButton
              disabled={createClientVersionMutation.isPending}
              isPending={createClientVersionMutation.isPending}
              type="submit"
            >
              创建版本
            </LoadingButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
