import { Card, CardContent, CardHeader } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Badge } from '@coozf/ui/components/badge'
import {
  RefreshCw,
  Play,
  Download,
  Monitor,
  Apple,
  AlertCircle,
  CheckCircle,
  Clock,
  Sparkles,
  RotateCcw,
} from 'lucide-react'
import { StatusBadge } from './status-badge'
import { useMemo, useRef, useState, useEffect } from 'react'
import { trpc } from '@/lib/trpc'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { PublicVersion } from '../tabs/client-management-tab'
import type { Application } from '../types'
import { CreateVersionDialog } from './create-versionDialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'

type Platform = 'WIN' | 'MAC'

interface UpdateStatus {
  platform: Platform
  needsBuild: boolean
  message: string
  latestPublicVersion?: PublicVersion
  latestClientVersion?: {
    id: string
    version: string
    buildStatus: string
    downloadUrl?: string
  }
  isChecking: boolean
}

const PLATFORMS: Array<{ key: Platform; name: string; icon: any }> = [
  { key: 'WIN', name: 'Windows', icon: Monitor },
  { key: 'MAC', name: 'macOS', icon: Apple },
]

interface ClientVersionsProps {
  applicationId: string
  application: Application
}

export function ClientVersions({ applicationId, application }: ClientVersionsProps) {
  const [updateStatus, setUpdateStatus] = useState<Record<Platform, UpdateStatus | null>>({
    WIN: null,
    MAC: null,
  })

  const [showCreateVersionDialog, setShowCreateVersionDialog] = useState(false)
  const [hasAutoChecked, setHasAutoChecked] = useState(false)

  const baseVersion = useRef<PublicVersion>(null)

  // 获取OEM配置信息
  const oemConfig = (application as any)?.oemConfig
  const hasOEMConfig = oemConfig && (oemConfig.name || oemConfig.logoUrl || oemConfig.packageName)

  // 获取客户端版本列表
  const clients = useQuery(
    trpc.clientVersion.list.queryOptions({
      applicationId,
      page: 1,
      pageSize: 10,
    }),
  )

  const checkUpdate = useMutation(
    trpc.clientVersion.checkUpdate.mutationOptions({
      onSuccess: (data, variables) => {
        setUpdateStatus((prev) => ({
          ...prev,
          [variables.platform]: {
            platform: variables.platform,
            needsBuild: data.hasUpdate,
            message: data.hasUpdate ? '有新版本可用' : '已是最新版本',
            latestPublicVersion: {
              ...data.latestVersion,
              platform: variables.platform,
            },
            latestClientVersion: {
              id: data.latestClientVersion?.baseVersion?.id,
              version: data.latestClientVersion?.baseVersion.version,
              buildStatus: data.latestClientVersion?.buildStatus,
              downloadUrl: data.latestClientVersion?.downloadUrl,
            },
            isChecking: false,
          },
        }))
      },
      onError: (_, variables) => {
        setUpdateStatus((prev) => ({
          ...prev,
          [variables.platform]: {
            platform: variables.platform,
            needsBuild: false,
            message: '检查更新失败',
            isChecking: false,
          },
        }))
      },
    }),
  )

  const clientVersions = useMemo(() => clients.data?.data, [clients.data])

  const handleCheckUpdate = async (platform: Platform) => {
    setUpdateStatus((prev) => ({
      ...prev,
      [platform]: { ...prev[platform], isChecking: true } as UpdateStatus,
    }))

    checkUpdate.mutate({ platform, applicationId })
  }

  const handleCreateVersion = async (platform: Platform) => {
    const status = updateStatus[platform]
    if (!status?.latestPublicVersion) return
    baseVersion.current = status.latestPublicVersion
    setShowCreateVersionDialog(true)
  }

  // 组件挂载时自动检查更新
  useEffect(() => {
    if (!hasAutoChecked) {
      // 延迟500ms后开始检查，给用户更好的视觉体验
      PLATFORMS.forEach((platform, index) => {
        // 错开检查时间，避免同时发起请求
        setTimeout(() => {
          setUpdateStatus((prev) => ({
            ...prev,
            [platform.key]: { ...prev[platform.key], isChecking: true } as UpdateStatus,
          }))
          checkUpdate.mutate({ platform: platform.key, applicationId })
        }, index * 300)
      })
      setHasAutoChecked(true)
    }
  }, [hasAutoChecked, checkUpdate, applicationId])

  return (
    <div className="space-y-4">
      {/* 极简标题 */}
      <div className="flex items-center gap-2 pb-2 border-b border-muted/30">
        <Sparkles className="w-4 h-4 text-primary" />
        <h1 className="text-lg font-semibold">客户端版本管理</h1>
      </div>

      {/* 主功能区域 - 平台更新检查 */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {PLATFORMS.map(({ key, name, icon: Icon }) => {
            const status = updateStatus[key]
            return (
              <Card
                key={key}
                className="relative overflow-hidden border border-border/50 shadow-sm hover:shadow-lg hover:border-primary/20 transition-all duration-300 bg-card/50 backdrop-blur-sm"
              >
                {/* 美化背景装饰 */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-full -translate-y-10 translate-x-10"></div>

                <CardHeader className="pb-2 relative z-10">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-primary/10 rounded-md">
                        <Icon className="w-4 h-4 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground">{name}</h3>
                        <p className="text-xs text-muted-foreground">桌面客户端</p>
                      </div>
                    </div>
                    <Badge
                      variant={status?.isChecking ? 'secondary' : 'outline'}
                      className={`px-2 py-0.5 text-xs ${status?.isChecking ? 'animate-pulse' : ''}`}
                    >
                      {status?.isChecking ? '检查中' : '就绪'}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="pt-0 space-y-3 relative z-10">
                  {status ? (
                    <>
                      {/* 紧凑状态指示器 */}
                      <div className="flex items-center gap-2 p-2 rounded-md bg-muted/30 border border-muted/50">
                        {!hasOEMConfig ? (
                          <>
                            <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full animate-pulse"></div>
                            <AlertCircle className="w-3.5 h-3.5 text-yellow-500" />
                            <span className="text-xs font-medium text-yellow-700 flex-1">需要配置OEM品牌信息</span>
                          </>
                        ) : status.needsBuild ? (
                          <>
                            <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-pulse"></div>
                            <AlertCircle className="w-3.5 h-3.5 text-orange-500" />
                            <span className="text-xs font-medium text-orange-700 flex-1">有新版本可构建</span>
                          </>
                        ) : !status.latestClientVersion ? (
                          <>
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                            <Play className="w-3.5 h-3.5 text-blue-500" />
                            <span className="text-xs font-medium text-blue-700 flex-1">可开始首次构建</span>
                          </>
                        ) : (
                          <>
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                            <CheckCircle className="w-3.5 h-3.5 text-green-500" />
                            <span className="text-xs font-medium text-green-700 flex-1">已是最新</span>
                            <span className="text-xs text-muted-foreground">可重新构建</span>
                          </>
                        )}
                      </div>

                      {/* 紧凑版本信息展示 */}
                      <div className="space-y-2">
                        {status.latestPublicVersion && (
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center gap-1.5">
                              <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                              <span className="text-muted-foreground">公用版本</span>
                            </div>
                            <Badge variant="secondary" className="font-mono text-xs h-5 px-1.5">
                              v{status.latestPublicVersion.version}
                            </Badge>
                          </div>
                        )}

                        {status.latestClientVersion && (
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center gap-1.5">
                              <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
                              <span className="text-muted-foreground">构建版本</span>
                            </div>
                            <div className="flex items-center gap-1.5">
                              <Badge variant="outline" className="font-mono text-xs h-5 px-1.5">
                                v{status.latestClientVersion.version}
                              </Badge>
                              <StatusBadge status={status.latestClientVersion.buildStatus as any} />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* 紧凑操作按钮组 */}
                      <div className="space-y-2">
                        {/* 没有OEM配置时的提示 */}
                        {!hasOEMConfig && status.latestPublicVersion && (
                          <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-center">
                            <div className="flex items-center justify-center gap-1.5 mb-1">
                              <AlertCircle className="w-3.5 h-3.5 text-yellow-600" />
                              <span className="text-xs font-medium text-yellow-700">需要先配置OEM品牌信息</span>
                            </div>
                            <p className="text-xs text-yellow-600">请前往"应用设置"页面配置</p>
                          </div>
                        )}

                        {/* 主操作按钮 - 合并检查更新到同一行 */}
                        <div className="flex gap-2">
                          {/* 正在构建中 */}
                          {status.latestClientVersion?.buildStatus === 'BUILDING' && (
                            <>
                              <Button variant="secondary" disabled size="sm" className="flex-1 h-8">
                                <RefreshCw className="w-3 h-3 mr-1.5 animate-spin" />
                                构建中
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCheckUpdate(key)}
                                disabled={status.isChecking}
                                className="h-8 px-3"
                              >
                                <RefreshCw className={`w-3 h-3 ${status.isChecking ? 'animate-spin' : ''}`} />
                              </Button>
                            </>
                          )}

                          {/* 有新版本可构建 */}
                          {status.needsBuild &&
                            status.latestPublicVersion &&
                            status.latestClientVersion?.buildStatus !== 'BUILDING' &&
                            hasOEMConfig && (
                              <>
                                <Button
                                  size="sm"
                                  className="flex-1 h-8 bg-gradient-to-r from-primary to-primary/80"
                                  onClick={() => handleCreateVersion(key)}
                                >
                                  <Play className="w-3 h-3 mr-1.5" />
                                  构建新版本
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCheckUpdate(key)}
                                  disabled={status.isChecking}
                                  className="h-8 px-3"
                                >
                                  <RefreshCw className={`w-3 h-3 ${status.isChecking ? 'animate-spin' : ''}`} />
                                </Button>
                              </>
                            )}

                          {/* 重新构建当前版本 */}
                          {!status.needsBuild &&
                            status.latestPublicVersion &&
                            status.latestClientVersion?.buildStatus !== 'BUILDING' &&
                            hasOEMConfig && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex-1 h-8"
                                  onClick={() => handleCreateVersion(key)}
                                >
                                  <RotateCcw className="w-3 h-3 mr-1.5" />
                                  重新构建
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCheckUpdate(key)}
                                  disabled={status.isChecking}
                                  className="h-8 px-3"
                                >
                                  <RefreshCw className={`w-3 h-3 ${status.isChecking ? 'animate-spin' : ''}`} />
                                </Button>
                              </>
                            )}

                          {/* 首次构建 */}
                          {!status.needsBuild &&
                            status.latestPublicVersion &&
                            !status.latestClientVersion &&
                            hasOEMConfig && (
                              <>
                                <Button
                                  size="sm"
                                  className="flex-1 h-8 bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                                  onClick={() => handleCreateVersion(key)}
                                >
                                  <Play className="w-3 h-3 mr-1.5" />
                                  首次构建
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCheckUpdate(key)}
                                  disabled={status.isChecking}
                                  className="h-8 px-3"
                                >
                                  <RefreshCw className={`w-3 h-3 ${status.isChecking ? 'animate-spin' : ''}`} />
                                </Button>
                              </>
                            )}

                          {/* 下载按钮 */}
                          {status.latestClientVersion?.downloadUrl &&
                            status.latestClientVersion.buildStatus === 'SUCCESS' && (
                              <Button variant="outline" asChild size="sm" className="h-8 px-3">
                                <a
                                  href={status.latestClientVersion.downloadUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <Download className="w-3 h-3" />
                                </a>
                              </Button>
                            )}
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCheckUpdate(key)}
                        disabled={false}
                        className="h-8"
                      >
                        <RefreshCw className="w-3 h-3 mr-1.5" />
                        检查更新
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* 紧凑构建历史区域 */}
      {clientVersions && clientVersions.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-muted/30">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <h3 className="text-sm font-medium">构建历史</h3>
            </div>
            <Badge variant="outline" className="text-xs h-5">
              {clientVersions.length}
            </Badge>
          </div>

          <Card className="border border-muted/30">
            <Table>
              <TableHeader>
                <TableRow className="border-muted/30 h-9">
                  <TableHead className="text-xs font-medium">版本</TableHead>
                  <TableHead className="text-xs font-medium">平台</TableHead>
                  <TableHead className="text-xs font-medium">状态</TableHead>
                  <TableHead className="text-xs font-medium">时间</TableHead>
                  <TableHead className="text-xs font-medium w-16">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {clientVersions.map((version) => (
                  <TableRow key={version.id} className="border-muted/20 hover:bg-muted/5 h-10">
                    <TableCell className="font-mono text-xs">v{version.baseVersion.version}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {version.platform === 'WIN' ? (
                          <Monitor className="w-3 h-3 text-muted-foreground" />
                        ) : (
                          <Apple className="w-3 h-3 text-muted-foreground" />
                        )}
                        <span className="text-xs">{version.platform === 'WIN' ? 'Win' : 'Mac'}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <StatusBadge status={version.buildStatus} />
                    </TableCell>
                    <TableCell className="text-xs text-muted-foreground">
                      {new Date(version.createdAt).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                    </TableCell>
                    <TableCell>
                      {version.buildStatus === 'SUCCESS' && version.downloadUrl && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="sm" variant="ghost" asChild className="h-6 w-6 p-0">
                              <Download className="w-4 h-4" />
                              <span className="sr-only">Toggle menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {Object.keys(version.downloadUrls).map((key) => (
                              <DropdownMenuItem key={key} asChild>
                                <a href={version.downloadUrls[key] as string} target="_blank" rel="noopener noreferrer">
                                  {key}
                                </a>
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </div>
      )}

      {/* 对话框 */}
      {baseVersion.current && (
        <CreateVersionDialog
          open={showCreateVersionDialog}
          onOpenChange={setShowCreateVersionDialog}
          applicationId={applicationId}
          baseVersion={baseVersion.current}
        />
      )}
    </div>
  )
}
