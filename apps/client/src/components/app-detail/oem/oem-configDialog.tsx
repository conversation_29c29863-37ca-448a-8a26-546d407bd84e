import { useForm, useController } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { LoadingButton } from '@coozf/ui/components/loading'
import { Upload, X, Image } from 'lucide-react'
import { OEMConfigSchema, type OEMConfig } from '@coozf/zod'
import { trpcClient } from '@/lib/trpc'
import { toast } from 'sonner'
interface OEMConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialConfig?: OEMConfig
  onSave: (config: OEMConfig) => void
  applicationId: string
}

export function OEMConfigDialog({ open, onOpenChange, initialConfig, onSave, applicationId }: OEMConfigDialogProps) {
  const [selectedLogo, setSelectedLogo] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(initialConfig?.logoUrl || null)
  const [isUploading, setIsUploading] = useState(false)

  const form = useForm<OEMConfig>({
    resolver: zodResolver(OEMConfigSchema),
    defaultValues: {
      name: initialConfig?.name || '',
      brandName: initialConfig?.brandName || '',
      logoUrl: initialConfig?.logoUrl,
      homeUrl: initialConfig?.homeUrl || '',
    },
  })

  const logoUrlController = useController({
    control: form.control,
    name: 'logoUrl',
  })

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件')
        return
      }

      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('图片文件不能超过5MB')
        return
      }

      setSelectedLogo(file)
      const previewUrl = URL.createObjectURL(file)
      setLogoPreview(previewUrl)
    }
  }

  const uploadLogo = async () => {
    if (!selectedLogo) return null

    setIsUploading(true)
    try {
      // 获取文件扩展名
      const fileExtension = selectedLogo.name.split('.').pop()?.toLowerCase() || 'png'

      // 确保扩展名是支持的图片格式
      const validExtensions = ['png', 'jpg', 'jpeg', 'svg', 'ico'] as const
      const extension = validExtensions.includes(fileExtension as any)
        ? (fileExtension as (typeof validExtensions)[number])
        : 'png'

      // 获取上传签名URL
      const { uploadUrl, accessUrl } = await trpcClient.oss.getUploadUrl.query({
        type: 'oem-logo',
        applicationId,
        logoType: 'main',
        extension,
      })

      // 上传文件
      const response = await fetch(uploadUrl, {
        method: 'PUT',
        body: selectedLogo,
      })

      if (!response.ok) {
        throw new Error('上传失败')
      }

      return accessUrl
    } catch (error) {
      console.error('Logo上传失败:', error)
      toast.error('Logo上传失败，请重试')
      return null
    } finally {
      setIsUploading(false)
    }
  }

  const removeLogo = () => {
    setSelectedLogo(null)
    setLogoPreview(null)
    logoUrlController.field.onChange('')
  }

  const onSubmit = async (data: OEMConfig) => {
    let logoUrl = data.logoUrl

    // 如果有新选择的logo，先上传
    if (selectedLogo) {
      const uploadedUrl = await uploadLogo()
      if (uploadedUrl) {
        logoUrl = uploadedUrl
        // 过滤掉空字符串，转换为undefined
      }
    }
    if (logoUrl) {
      const config: OEMConfig = {
        ...data,
        logoUrl: logoUrl || undefined,
        homeUrl: data.homeUrl || undefined,
      }

      onSave(config)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>品牌定制配置</DialogTitle>
          <DialogDescription>设置您的品牌信息和客户端外观</DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">应用名称 *</Label>
            <Input id="name" {...form.register('name')} placeholder="我的定制客户端" />
            {form.formState.errors.name && (
              <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="brandName">品牌名称 * (唯一)</Label>
            <Input id="brandName" {...form.register('brandName')} placeholder="MyApp" />
            {form.formState.errors.brandName && (
              <p className="text-sm text-destructive">{form.formState.errors.brandName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label>Logo 设置</Label>

            {/* Logo 预览区域 */}
            <div className="border-2 border-dashed border-gray-200 rounded-lg p-4">
              {logoPreview ? (
                <div className="relative">
                  <div className="flex items-center gap-3">
                    <div className="w-16 h-16 bg-gray-50 rounded-lg flex items-center justify-center overflow-hidden">
                      <img src={logoPreview} alt="Logo preview" className="w-full h-full object-contain" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium truncate w-48">
                        {selectedLogo ? selectedLogo.name : 'Current Logo'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {selectedLogo ? `${(selectedLogo.size / 1024).toFixed(1)} KB` : '当前logo'}
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={removeLogo}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <Image className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">暂无Logo</p>
                </div>
              )}
            </div>

            {/* 文件选择和URL输入 */}
            <div className="space-y-3">
              <div>
                <Label htmlFor="logo-file" className="cursor-pointer">
                  <div className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800">
                    <Upload className="w-4 h-4" />
                    上传新Logo
                  </div>
                </Label>
                <Input id="logo-file" type="file" accept="image/*" onChange={handleLogoChange} className="hidden" />
              </div>

              <div className="text-xs text-gray-500 space-y-1">
                <p>• 支持 PNG、JPG、SVG 格式</p>
                <p>• 建议尺寸：512x512px</p>
              </div>
            </div>

            {form.formState.errors.logoUrl && (
              <p className="text-sm text-destructive">{form.formState.errors.logoUrl.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="homeUrl">首页地址</Label>
            <Input id="homeUrl" {...form.register('homeUrl')} placeholder="https://example.com" type="url" />
            {form.formState.errors.homeUrl && (
              <p className="text-sm text-destructive">{form.formState.errors.homeUrl.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <LoadingButton
              type="submit"
              isPending={isUploading || form.formState.isSubmitting}
              disabled={isUploading || form.formState.isSubmitting}
            >
              {isUploading ? '上传中...' : '保存配置'}
            </LoadingButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
