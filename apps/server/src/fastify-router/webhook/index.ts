import type { FastifyInstance } from 'fastify'
import { xiaohongshuWebhookRoutes } from './xiaohongshu'
import { crawlerWebhookRoutes } from './crawler'
import { gitlabWebhookRoutes } from './gitlab'

const routes = async (app: FastifyInstance) => {
  app.register(xiaohongshuWebhookRoutes, { prefix: '/xiaohongshu' })
  app.register(crawlerWebhookRoutes, { prefix: '/web' })
  app.register(gitlabWebhookRoutes, { prefix: '/gitlab' })
}

export default routes
