import { env } from '@/env'
import { ClientBuildStatus, db } from '@coozf/db'
import { gitlabWebhookSchema, pipelienWebhookSchema } from '@coozf/zod'
import { FastifyInstance } from 'fastify'

export async function gitlabWebhookRoutes(app: FastifyInstance) {
  app.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers['x-gitlab-token']
    if (authHeader !== env.GIT_WEBHOOK_SECRET) {
      _reply.status(403).send('Forbidden')
    }
  })
  app.post('/', async (request, reply) => {
    const body = gitlabWebhookSchema.parse(request.body)
    if (body.object_kind === 'pipeline') {
      const pipelien = pipelienWebhookSchema.parse(request.body)
      // 查找pipeline记录
      if (pipelien.object_attributes.source === 'trigger') {
        const version = await db.clientVersion.findFirst({
          where: { gitlabPipelineId: pipelien.object_attributes.id },
        })
        if (version) {
          await db.clientVersion.update({
            where: { id: version.id },
            data: {
              buildStartedAt: pipelien.object_attributes.created_at,
              buildCompletedAt: pipelien.object_attributes.finished_at,
              buildStatus: convertGitLabStatusToClientStatus(pipelien.object_attributes.status as ********************),
            },
          })
        }
      }
    }
    return reply.send({ success: true })
  })
}

type ******************** =
  | 'created'
  | 'waiting_for_resource'
  | 'preparing'
  | 'pending'
  | 'running'
  | 'success'
  | 'failed'
  | 'canceled'
  | 'skipped'
  | 'manual'

// 转换状态
function convertGitLabStatusToClientStatus(gitlabStatus: ********************): ClientBuildStatus {
  switch (gitlabStatus) {
    case 'created':
    case 'waiting_for_resource':
    case 'preparing':
    case 'pending':
    case 'manual':
      return ClientBuildStatus.PENDING

    case 'running':
      return ClientBuildStatus.BUILDING

    case 'success':
      return ClientBuildStatus.SUCCESS

    case 'failed':
    case 'skipped':
      return ClientBuildStatus.FAILED

    case 'canceled':
      return ClientBuildStatus.CANCELLED

    default:
      // 兜底处理，对于未知状态默认返回PENDING
      return ClientBuildStatus.PENDING
  }
}
