import { storageService } from '@coozf/huoshan'
import { protectedProcedure } from '@/procedure'
import { router } from '@/trpc'
import { simpleUploadInputSchema, accessInputSchema } from '@coozf/zod'
import { env } from '@/env'

export const ossRouter = router({
  // 简化的文件上传接口
  getUploadUrl: protectedProcedure
    .input(simpleUploadInputSchema)
    .use(async ({ input, ctx, next }) => {
      // OEM Logo 类型需要应用权限验证
      if (input.type === 'oem-logo') {
        const application = await ctx.db.application.findUnique({
          where: {
            id: input.applicationId,
            userId: ctx.user.id,
          },
        })

        if (!application) {
          throw new Error('应用不存在或无权限访问')
        }

        return next({
          ctx: {
            ...ctx,
            application,
          },
        })
      }

      return next()
    })
    .query(async ({ input, ctx }) => {
      const timestamp = Date.now()
      let filePath: string

      // 根据类型生成文件路径
      switch (input.type) {
        case 'oem-logo': {
          const { applicationId, logoType, extension } = input
          filePath = `oem-assets/${applicationId}/logos/${logoType}/current.${extension}`
          break
        }

        case 'avatar': {
          const { extension } = input
          filePath = `user-files/${ctx.user.id}/avatar/current.${extension}`
          break
        }

        case 'file': {
          const { category, extension, customPath } = input
          if (customPath) {
            // 使用自定义路径，但仍然限制在用户目录下
            const safePath = customPath.replace(/[^a-zA-Z0-9\-_\/\.]/g, '_')
            filePath = `user-files/${ctx.user.id}/${safePath}-${timestamp}.${extension}`
          } else {
            filePath = `user-files/${ctx.user.id}/${category}/${timestamp}.${extension}`
          }
          break
        }

        default:
          throw new Error('不支持的文件类型')
      }

      // 获取上传签名URL
      const res = await storageService.getUploadUrl(filePath, undefined, env.OSS_DESKTOP_BUCKET)

      return {
        filePath,
        ...res,
      }
    }),

  // 获取文件访问URL
  getAccessUrl: protectedProcedure.input(accessInputSchema).query(async ({ input, ctx }) => {
    const { filePath, expires } = input

    // 权限检查：用户只能访问自己的文件或公共资源
    const isUserFile = filePath.startsWith(`user-files/${ctx.user.id}/`)
    const isOemFile = filePath.startsWith('oem-assets/')

    if (!isUserFile && !isOemFile) {
      throw new Error('无权限访问该文件')
    }

    // OEM 文件需要额外验证应用权限
    if (isOemFile) {
      const applicationId = filePath.split('/')[1]
      const application = await ctx.db.application.findUnique({
        where: {
          id: applicationId,
          userId: ctx.user.id,
        },
      })

      if (!application) {
        throw new Error('无权访问该OEM资源')
      }
    }

    // 获取带签名的访问URL
    const accessUrl = await storageService.getAccessUrl(filePath, expires)

    return {
      accessUrl,
      expiresIn: expires,
    }
  }),
})
