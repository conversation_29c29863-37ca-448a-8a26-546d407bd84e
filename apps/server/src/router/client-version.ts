import { z } from 'zod'
import { router } from '@/trpc'
import { ClientVersionService } from '@/lib/business/client-version'
import { ApplicationService } from '@/lib/business/application'
import { applicationProcedure } from '@/procedure'
import {
  CreateClientVersionSchema,
  ClientVersionListSchema,
  OEMConfigSchema,
  PlatformSchema,
  CheckUpdateWithDesktopSchema,
} from '@coozf/zod'
import { TRPCError } from '@trpc/server'
import { VersionService } from '@/lib'

export const userClientVersionRouter = router({
  // 用户创建自己应用的客户端版本
  create: applicationProcedure.input(CreateClientVersionSchema).mutation(async ({ ctx, input }) => {
    if (!ctx.application.oemEnabled) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用未启用OEM功能',
      })
    }
    return await ClientVersionService.createClientVersion(
      { ...input, applicationId: ctx.application.id },
      ctx.application,
    )
  }),

  // 用户获取自己应用的客户端版本列表
  list: applicationProcedure.input(ClientVersionListSchema).query(({ input }) => {
    return ClientVersionService.getClientVersionList(input)
  }),

  // 获取公用最新版本
  getLatestPublicVersion: applicationProcedure
    .input(z.object({ platform: PlatformSchema }))
    .query(async ({ input, ctx }) => {
      return ctx.db.version.findFirst({
        where: {
          type: 'DESKTOP',
          platform: input.platform,
          isActive: true,
        },
        orderBy: { createdAt: 'desc' },
      })
    }),

  // 用户删除自己的客户端版本
  delete: applicationProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    return await ClientVersionService.deleteClientVersion(input.id, ctx.application.id)
  }),

  // 用户获取自己的客户端版本详情
  getById: applicationProcedure.input(z.object({ id: z.string() })).query(async ({ input, ctx }) => {
    return await ClientVersionService.getClientVersionById(input.id, ctx.application.id)
  }),

  // 检查更新 - 对比指定平台的最新公用版本与应用最新构建版本
  checkUpdate: applicationProcedure.input(CheckUpdateWithDesktopSchema).mutation(async ({ input, ctx }) => {
    const res = await ctx.db.clientVersion.findFirst({
      where: {
        applicationId: ctx.application.id,
        platform: input.platform,
        buildStatus: { in: ['SUCCESS', 'BUILDING'] }, // 包含BUILDING状态
      },
      orderBy: { createdAt: 'desc' },
      include: {
        baseVersion: {
          select: {
            id: true,
            version: true,
          },
        },
      },
    })
    const update = await VersionService.checkForUpdate({
      type: 'DESKTOP',
      platform: input.platform,
      version: res?.baseVersion.version,
    })
    return {
      ...update,
      latestClientVersion: res,
    }
  }),

  // 更新应用的OEM配置
  updateOEMConfig: applicationProcedure.input(OEMConfigSchema).mutation(async ({ input }) => {
    const { applicationId, ...config } = input
    // 更新OEM配置和品牌名称
    return await ApplicationService.updateApplication(applicationId, {
      oemConfig: config,
      brandName: config.brandName, // 将shortName同步到brandName字段
    })
  }),
})
