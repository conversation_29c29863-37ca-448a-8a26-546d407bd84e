import { db } from '@coozf/db'
import { TRPCError } from '@trpc/server'
import { Application, Prisma } from '@prisma/client'
import {
  CreateClientVersionInput,
  UpdateClientVersionBuildStatusInput,
  ClientVersionListParams,
  Platform,
  OEMConfigSchema,
  OEMConfig,
} from '@coozf/zod'
import { messagePublisher } from '../services/message-publisher'
import { paginate } from '../utils'
import { env } from '@/env'
import { gitlabPipelineTrigger, type PipelineResponse } from '../services/gitlab-pipeline-trigger'
import { VersionService } from './version'

/**
 * 客户端版本管理业务逻辑
 */
export class ClientVersionService {
  /**
   * 创建客户端版本
   */
  static async createClientVersion(
    data: CreateClientVersionInput & { applicationId: string; platform: Platform },
    app: Application,
  ) {
    const { applicationId, baseVersionId, description, platform } = data

    // 验证基础版本是否存在
    const baseVersion = await db.version.findUnique({
      where: { id: baseVersionId },
    })

    if (!baseVersion) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '基础版本不存在',
      })
    }

    const oemConfig = OEMConfigSchema.parse(app.oemConfig)

    // TODO: 这里应该调用GitLab API触发构建
    const pipeline = await this.triggerGitBuild(oemConfig, baseVersion.version, platform)

    // 创建客户端版本
    const clientVersion = await db.clientVersion.create({
      data: {
        applicationId,
        baseVersionId,
        platform,
        buildStatus: 'PENDING',
        gitlabPipelineId: pipeline.id,
        gitlabProjectId: pipeline.project_id,
        gitlabRef: pipeline.ref,
        gitlabCommitSha: pipeline.sha,
        gitlabWebUrl: pipeline.web_url,
        description: description || baseVersion.description,
        downloadUrl: VersionService.generateDesktopDownloadUrl(baseVersion.version, app.brandName || undefined),
      },
      include: {
        application: true,
        baseVersion: true,
      },
    })

    return clientVersion
  }

  /**
   * 获取客户端版本列表
   */
  static async getClientVersionList(params: ClientVersionListParams & { applicationId: string }) {
    const { page, pageSize, applicationId, buildStatus, platform } = params

    // 构建查询条件
    const where = {
      ...(applicationId ? { applicationId } : {}),
      ...(buildStatus ? { buildStatus } : {}),
      ...(platform ? { platform } : {}),
    }

    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal: () => db.clientVersion.count({ where }),
        getItems: (skip, take) =>
          db.clientVersion.findMany({
            where,
            skip,
            take,
            orderBy: { createdAt: 'desc' },
            include: {
              baseVersion: {
                select: {
                  id: true,
                  version: true,
                },
              },
              application: {
                select: {
                  brandName: true,
                },
              },
            },
          }),
        transform(item) {
          return {
            ...item,
            downloadUrls: VersionService.getDesktopPackage(
              item.baseVersion.version,
              item.platform,
              item.application.brandName!,
            ),
          }
        },
      },
    )
  }

  /**
   * 更新构建状态
   */
  static async updateBuildStatus(data: UpdateClientVersionBuildStatusInput) {
    const { id, ...updateData } = data

    const clientVersion = await db.clientVersion.findUnique({
      where: { id },
      include: {
        application: true,
      },
    })

    if (!clientVersion) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '客户端版本不存在',
      })
    }

    // 更新构建状态
    const updatedVersion = await db.clientVersion.update({
      where: { id },
      data: updateData,
      include: {
        baseVersion: {
          select: {
            id: true,
            version: true,
            type: true,
          },
        },
      },
    })

    // 如果构建成功，发布更新消息
    if (updateData.buildStatus === 'SUCCESS' && updateData.downloadUrl) {
      try {
        await messagePublisher.publishVersionUpdate({
          type: updatedVersion.baseVersion.type,
          version: updatedVersion.baseVersion.version,
          forceUpdate: false, // OEM版本暂不支持强制更新
          description: updatedVersion.description || '',
          downloadUrl: updateData.downloadUrl,
          timestamp: Date.now(),
          publishedBy: 'system', // 系统自动发布
          applicationId: updatedVersion.applicationId, // 新增应用ID用于区分
        })
      } catch (error) {
        console.error('发布客户端版本更新消息失败:', error)
      }
    }

    return updatedVersion
  }

  /**
   * 删除客户端版本
   */
  static async deleteClientVersion(id: string, applicationId: string) {
    const clientVersion = await db.clientVersion.findFirst({
      where: {
        id,
        applicationId,
      },
    })

    if (!clientVersion) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '客户端版本不存在',
      })
    }

    // 只允许删除未成功构建的版本
    if (clientVersion.buildStatus === 'SUCCESS') {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '无法删除已成功构建的版本',
      })
    }

    return await db.clientVersion.delete({
      where: { id },
    })
  }

  /**
   * 获取客户端版本详情
   */
  static async getClientVersionById(id: string, applicationId: string) {
    const clientVersion = await db.clientVersion.findFirst({
      where: {
        id,
        applicationId,
      },
      include: {
        application: {
          select: {
            id: true,
            name: true,
            oemConfig: true,
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        baseVersion: {
          select: {
            id: true,
            version: true,
            type: true,
            platform: true,
            description: true,
          },
        },
      },
    })

    if (!clientVersion) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '客户端版本不存在',
      })
    }

    return clientVersion
  }

  /**
   * 触发构建任务
   */
  static async triggerBuild(clientVersionId: string, userId: string) {
    const clientVersion = await db.clientVersion.findFirst({
      where: {
        id: clientVersionId,
        application: {
          userId: userId,
        },
      },
      include: {
        application: true,
        baseVersion: true,
      },
    })

    if (!clientVersion) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '客户端版本不存在',
      })
    }

    if (clientVersion.buildStatus === 'BUILDING') {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '版本正在构建中，请勿重复触发',
      })
    }

    if (clientVersion.buildStatus === 'SUCCESS') {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '版本已构建成功，无需重复构建',
      })
    }

    // 更新状态为构建中
    const updatedVersion = await db.clientVersion.update({
      where: { id: clientVersionId },
      data: {
        buildStatus: 'BUILDING',
        buildStartedAt: new Date(),
        buildLog: null,
        errorMessage: null,
      },
    })

    // TODO: 这里应该调用GitLab API触发构建
    // await this.triggerGitLabBuild(clientVersion)

    return updatedVersion
  }

  /**
   * 触发构建任务
   */
  static async triggerGitBuild(oemConfig: OEMConfig, version: string, platform: Platform): Promise<PipelineResponse> {
    // TODO: 这里应该调用GitLab API触发构建
    const pipeline = await gitlabPipelineTrigger.triggerPipelineWithToken({
      gitlabDomain: env.GITLAB_DOMAIN,
      projectId: env.GITLAB_PROJECT_ID, // 或 'mygroup/myproject'
      token: env.GITLAB_ACCESS_TOKEN, // 在 GitLab 项目设置中创建
      ref: env.MODE === 'prod' ? `v${version}-production-open` : `v${version}-beta-open.1`, // 或标签名 'v4.8.29-oem-qdy'
      variables: {
        ...oemConfig,
        TRIGGER_PLATFORM: platform === 'WIN' ? 'windows' : 'darwin',
      },
    })
    return pipeline
    // await giteaClient.setRepositoryVariable('buildArgs', JSON.stringify(oemConfig))
    // await giteaClient.createTagFromOldTag(
    //   env.MODE === 'prod' ? `v${version}-production-open` : `v${version}-beta-open.1`,
    //   `v${version}-oem-${oemConfig.shortName}`,
    //   description,
    // )
  }
}
