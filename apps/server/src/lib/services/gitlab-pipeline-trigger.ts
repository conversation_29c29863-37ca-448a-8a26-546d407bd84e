// src/services/GitLabPipelineTrigger.ts

import { logger } from "./logger"

interface TriggerPipelineOptions {
  gitlabDomain: string // 例如 'https://gitlab.com'
  projectId: string | number // 项目 ID 或 URL 编码的路径
  token: string // 项目的 trigger token 或 personal access token
  ref: string // 分支、标签或 commit SHA
  variables?: Record<string, string> // Pipeline 变量
}

export interface PipelineResponse {
  id: number
  project_id: number
  status: string
  ref: string
  sha: string
  web_url: string
  created_at: string
  variables: Array<{ key: string; value: string }>
}

export class GitLabPipelineTrigger {
  /**
   * 使用 Trigger Token 触发 Pipeline（推荐）
   */
  public async triggerPipelineWithToken(options: TriggerPipelineOptions): Promise<PipelineResponse> {
    const { gitlabDomain, projectId, token, ref, variables = {} } = options

    // 构建表单数据
    const formData = new URLSearchParams()
    formData.append('token', token)
    formData.append('ref', ref)

    // 添加变量
    Object.entries(variables).forEach(([key, value]) => {
      formData.append(`variables[${key}]`, value)
    })

    const url = `${gitlabDomain}/api/v4/projects/${encodeURIComponent(projectId)}/trigger/pipeline`

    logger.info(`[Pipeline] Triggering pipeline for ref: ${ref}`)
    logger.info(`[Pipeline] Variables:`, variables)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString(),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Failed to trigger pipeline: ${response.status} - ${error}`)
      }

      const pipeline = (await response.json()) as PipelineResponse
      logger.info(`[Pipeline] Successfully triggered! Pipeline ID: ${pipeline.id}`)
      logger.info(`[Pipeline] URL: ${pipeline.web_url}`)

      return pipeline
    } catch (error) {
      console.error('[Pipeline] Error triggering pipeline:', error)
      throw error
    }
  }

  /**
   * 使用 Personal Access Token 触发 Pipeline
   */
  public async triggerPipelineWithPAT(options: TriggerPipelineOptions): Promise<PipelineResponse> {
    const { gitlabDomain, projectId, token, ref, variables = {} } = options

    const url = `${gitlabDomain}/api/v4/projects/${encodeURIComponent(projectId)}/pipeline`

    logger.info(`[Pipeline] Triggering pipeline for ref: ${ref}`)
    logger.info(`[Pipeline] Variables:`, variables)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ref,
          variables: Object.entries(variables).map(([key, value]) => ({
            key,
            value,
            variable_type: 'env_var',
          })),
        }),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Failed to trigger pipeline: ${response.status} - ${error}`)
      }

      const pipeline = (await response.json()) as PipelineResponse
      logger.info(`[Pipeline] Successfully triggered! Pipeline ID: ${pipeline.id}`)
      logger.info(`[Pipeline] URL: ${pipeline.web_url}`)

      return pipeline
    } catch (error) {
      console.error('[Pipeline] Error triggering pipeline:', error)
      throw error
    }
  }

  /**
   * 创建标签并触发 Pipeline
   */
  public async createTagAndTriggerPipeline(options: {
    gitlabDomain: string
    projectId: string | number
    privateToken: string // Personal Access Token
    triggerToken: string // Pipeline Trigger Token
    tagName: string
    ref: string // commit SHA 或分支名
    message?: string
    variables?: Record<string, string>
  }): Promise<{ tag: any; pipeline: PipelineResponse }> {
    const { gitlabDomain, projectId, privateToken, triggerToken, tagName, ref, message, variables } = options

    // 1. 创建标签
    logger.info(`[Tag] Creating tag ${tagName} on ref ${ref}...`)
    const tagUrl = `${gitlabDomain}/api/v4/projects/${encodeURIComponent(projectId)}/repository/tags`

    const tagResponse = await fetch(tagUrl, {
      method: 'POST',
      headers: {
        'PRIVATE-TOKEN': privateToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tag_name: tagName,
        ref: ref,
        message: message || `Release ${tagName}`,
      }),
    })

    if (!tagResponse.ok) {
      const error = await tagResponse.text()
      throw new Error(`Failed to create tag: ${tagResponse.status} - ${error}`)
    }

    const tag = await tagResponse.json()
    logger.info(`[Tag] Successfully created tag: ${tagName}`)

    // 2. 触发 Pipeline
    const pipeline = await this.triggerPipelineWithToken({
      gitlabDomain,
      projectId,
      token: triggerToken,
      ref: tagName,
      variables,
    })

    return { tag, pipeline }
  }

  /**
   * 获取 Pipeline 状态
   */
  public async getPipelineStatus(options: {
    gitlabDomain: string
    projectId: string | number
    pipelineId: number
    token: string // Personal Access Token
  }): Promise<PipelineResponse> {
    const { gitlabDomain, projectId, pipelineId, token } = options

    const url = `${gitlabDomain}/api/v4/projects/${encodeURIComponent(projectId)}/pipelines/${pipelineId}`

    const response = await fetch(url, {
      headers: {
        'PRIVATE-TOKEN': token,
      },
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to get pipeline status: ${response.status} - ${error}`)
    }

    return await response.json() as PipelineResponse
  }

  /**
   * 等待 Pipeline 完成
   */
  public async waitForPipeline(options: {
    gitlabDomain: string
    projectId: string | number
    pipelineId: number
    token: string
    timeoutMs?: number
    intervalMs?: number
  }): Promise<PipelineResponse> {
    const {
      gitlabDomain,
      projectId,
      pipelineId,
      token,
      timeoutMs = 30 * 60 * 1000, // 默认 30 分钟
      intervalMs = 10 * 1000, // 默认 10 秒
    } = options

    const startTime = Date.now()

    while (Date.now() - startTime < timeoutMs) {
      const pipeline = await this.getPipelineStatus({
        gitlabDomain,
        projectId,
        pipelineId,
        token,
      })

      logger.info(`[Pipeline] Status: ${pipeline.status}`)

      // 完成状态
      if (['success', 'failed', 'canceled', 'skipped'].includes(pipeline.status)) {
        return pipeline
      }

      // 等待下次检查
      await new Promise((resolve) => setTimeout(resolve, intervalMs))
    }

    throw new Error(`Pipeline timeout after ${timeoutMs}ms`)
  }
}

export const gitlabPipelineTrigger = new GitLabPipelineTrigger()
