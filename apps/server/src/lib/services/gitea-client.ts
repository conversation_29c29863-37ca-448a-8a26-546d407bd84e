// gitea-deployment.service.ts

import axios, { AxiosInstance } from 'axios'
import { logger } from './logger'
import { env } from '@/env'
// import { gitPusher } from './git-pusher'

// ==================== 类型定义 ====================
interface GiteaConfig {
  baseUrl: string
  token: string
  owner: string
  repo: string
  webhookSecret?: string
}

// ==================== Gitea API 客户端 ====================
class GiteaClient {
  private client: AxiosInstance
  private logger = logger
  private owner: string
  private repo: string

  constructor(private config: GiteaConfig) {
    this.client = axios.create({
      baseURL: `${config.baseUrl}/api/v1`,
      headers: {
        Authorization: `token ${config.token}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    })
    this.owner = config.owner
    this.repo = config.repo
  }

  // 设置仓库变量
  async setRepositoryVariable(name: string, value: string) {
    try {
      const response = await this.client.put(`/repos/${this.owner}/${this.repo}/actions/variables/${name}`, { value })
      this.logger.info('Repository variable set', { variable: name })
      return response.data
    } catch (error) {
      this.logger.error('Failed to set repository variable', { error, variable: name })
      throw error
    }
  }

  // 获取默认分支
  async getDefaultBranch(): Promise<string> {
    const response = await this.client.get(`/repos/${this.owner}/${this.repo}`)
    return response.data.default_branch || 'main'
  }

  // 获取分支最新提交
  async getBranchCommit(branch: string) {
    const response = await this.client.get(`/repos/${this.owner}/${this.repo}/branches/${branch}`)
    return response.data.commit
  }

  // 创建标签
  async createTag(tag: string, sha: string, message?: string) {
    try {
      const response = await this.client.post(`/repos/${this.owner}/${this.repo}/tags`, {
        tag_name: tag,
        target: sha,
        message: message || `Release ${tag}`,
      })
      // await gitPusher.pushTag({
      //   giteaDomain: env.GITEA_URL,
      //   owner: this.owner,
      //   repo: this.repo,
      //   token: this.config.token,
      //   tagName: tag,
      //   targetSha: sha,
      //   message,
      // })
      this.logger.info('Tag created successfully', { tag })
      return response.data
    } catch (error) {
      this.logger.error('Failed to create tag', { error, tag })
      throw error
    }
  }

  // 根据旧tag创建新tag
  async createTagFromOldTag(oldTag: string, newTag: string, message?: string) {
    const res = await this.client.get(`/repos/${this.owner}/${this.repo}/tags/${oldTag}`)
    // return await this.client.post(`/repos/${this.owner}/${this.repo}/releases`, {
    //   tag_name: newTag, // 要创建的 tag 名称
    //   target_commitish: res.data.commit.sha, // tag 指向的 commit SHA 或分支名
    //   name: `Release ${newTag}`, // Release 的标题
    //   body: message || `This is the official release for version ${newTag}.`, // Release 的描述
    //   draft: false, // 不是草稿
    //   prerelease: false, // 不是预发布
    // })
    return this.createTag(newTag, res.data.commit.sha, message)
  }

  // 获取 Actions 运行列表
  async getActionRuns(owner: string, repo: string, limit: number = 10) {
    const response = await this.client.get(`/repos/${owner}/${repo}/actions/runs?limit=${limit}`)
    return response.data
  }

  // 获取特定运行状态
  async getActionRun(owner: string, repo: string, runId: number) {
    const response = await this.client.get(`/repos/${owner}/${repo}/actions/runs/${runId}`)
    return response.data
  }
}

export const giteaClient = new GiteaClient({
  baseUrl: env.GITEA_URL,
  token: env.GITEA_ACCESS_TOKEN,
  owner: 'lj',
  repo: 'desktop',
})
